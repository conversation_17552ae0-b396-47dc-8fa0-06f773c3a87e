@echo off
echo 开始构建 Windows 应用...

REM 设置代码页为 UTF-8 以支持中文显示
chcp 65001 >nul

REM 清理之前的构建
echo 清理构建缓存...
flutter clean

REM 获取依赖
echo 获取依赖包...
flutter pub get

REM 检查依赖获取是否成功
if %ERRORLEVEL% NEQ 0 (
    echo 依赖获取失败！请检查网络连接。
    pause
    exit /b 1
)

REM 构建 Windows 应用
echo 构建 Windows 应用...
flutter build windows

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 构建成功！
    echo 可执行文件位置: build\windows\x64\runner\Release\novel_app.exe
    echo ========================================
    echo.
    
    REM 检查文件是否存在
    if exist "build\windows\x64\runner\Release\novel_app.exe" (
        echo 应用文件确认存在。
        
        REM 询问是否运行应用
        set /p run_app="是否立即运行应用？(y/n): "
        if /i "%run_app%"=="y" (
            echo 启动应用...
            start "" "build\windows\x64\runner\Release\novel_app.exe"
        )
    ) else (
        echo 警告：应用文件未找到！
    )
) else (
    echo.
    echo ========================================
    echo 构建失败！请检查错误信息。
    echo 常见解决方案：
    echo 1. 检查网络连接
    echo 2. 确保 Visual Studio 2022 已安装
    echo 3. 确保 Windows SDK 已安装
    echo 4. 运行 flutter doctor 检查环境
    echo ========================================
    echo.
)

pause
