# Windows 构建故障排除指南

## 问题解决方案

### 1. CMake 安装错误

**错误信息：**
```
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppCommon.targets(166,5): error MSB3073: 命令"setlocal
```

**解决方案：**
1. 清理构建缓存：`flutter clean`
2. 重新获取依赖：`flutter pub get`
3. 重新构建：`flutter build windows`

### 2. 网络连接问题

**错误信息：**
```
A cryptographic error occurred while checking "https://storage.googleapis.com/"
```

**解决方案：**
1. 检查网络连接
2. 如果使用代理，确保代理配置正确
3. 尝试使用移动热点或其他网络

### 3. Visual Studio 配置问题

**解决方案：**
1. 确保安装了 Visual Studio 2022 Community 或更高版本
2. 确保安装了以下组件：
   - MSVC v143 - VS 2022 C++ x64/x86 build tools
   - Windows 10/11 SDK
   - CMake tools for Visual Studio

### 4. Flutter 环境检查

运行以下命令检查环境：
```bash
flutter doctor -v
```

确保以下项目显示为 ✓：
- Flutter
- Windows Version
- Visual Studio
- Connected device (Windows)

## 成功构建的标志

构建成功后，您应该看到：
```
√ Built build\windows\x64\runner\Release\novel_app.exe
```

构建产物位置：
- 主程序：`build\windows\x64\runner\Release\novel_app.exe`
- 依赖库：`build\windows\x64\runner\Release\*.dll`
- 资源文件：`build\windows\x64\runner\Release\data\`

## 快速构建脚本

使用 `build_simple.bat` 脚本进行一键构建：
```bash
.\build_simple.bat
```

该脚本会自动：
1. 清理构建缓存
2. 获取依赖
3. 构建应用
4. 提供运行选项

## 常见问题

### Q: 构建时间很长怎么办？
A: 首次构建需要下载依赖，可能需要几分钟。后续构建会更快。

### Q: 构建后应用无法启动？
A: 检查是否缺少 Visual C++ 运行时库，可以安装 `windows\VC_redist.x64.exe`

### Q: 如何分发应用？
A: 需要将整个 `build\windows\x64\runner\Release\` 目录打包分发，或使用 `build_windows.bat` 创建安装包。

## 联系支持

如果问题仍然存在，请提供：
1. 完整的错误信息
2. `flutter doctor -v` 的输出
3. 操作系统版本信息
